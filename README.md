# Maper - محول PDF إلى خريطة عقل

تطبيق احترافي لتحويل مستندات PDF إلى خرائط عقل تفاعلية وجذابة بصرياً.

## المميزات الرئيسية

### 🔄 تحويل PDF
- استيراد ملفات PDF وتحليلها تلقائياً
- استخراج النصوص والعناوين والهيكل من المستندات
- تحويل ذكي للمحتوى إلى تنسيق خريطة عقل هرمية
- دعم أنواع مختلفة من ملفات PDF

### 🎨 تصميم احترافي
- واجهة مستخدم حديثة ومتجاوبة
- رسوميات عالية الجودة وحركات سلسة
- أنماط ألوان قابلة للتخصيص
- رسوميات متجهة للعرض الواضح على جميع أحجام الشاشات

### 🗺️ مميزات خريطة العقل
- تخطيطات متعددة (شعاعي، شجري، تنظيمي، مخطط انسيابي)
- تحرير العقد بالسحب والإفلات
- إضافة/تحرير/حذف العقد والاتصالات
- خيارات تنسيق نص غنية للعقد
- دعم الأيقونات والصور للتحسين البصري

### 📱 متطلبات تقنية
- تطوير Flutter أصلي
- أداء محسن للتعامل مع ملفات PDF الكبيرة
- وظائف غير متصلة (لا يتطلب إنترنت للمميزات الأساسية)
- نظام إدارة ملفات لحفظ وتنظيم المشاريع
- تصميم متجاوب للأجهزة اللوحية والهواتف

## كيفية الاستخدام

### 1. استيراد PDF
- اضغط على زر "استيراد PDF" في الشاشة الرئيسية
- اختر ملف PDF من جهازك
- اختر نمط التخطيط والألوان المفضل
- اضغط "إنشاء خريطة عقل"

### 2. تحرير خريطة العقل
- اضغط على زر "تحرير" لتفعيل وضع التحرير
- اضغط على عقدة لتحديدها
- اضغط مرتين على عقدة لتحريرها
- اسحب العقد لإعادة ترتيبها
- استخدم الأزرار العائمة لإضافة أو حذف العقد

### 3. تخصيص المظهر
- اضغط على أيقونة الإعدادات في شريط الأدوات
- اختر من بين تخطيطات مختلفة
- غير نمط الألوان حسب تفضيلك

### 4. حفظ ومشاركة
- احفظ خريطة العقل للوصول إليها لاحقاً
- صدر كصورة أو ملف JSON
- شارك خريطة العقل مع الآخرين

## البناء والتشغيل

### متطلبات النظام
- Flutter SDK 3.7.2 أو أحدث
- Dart 3.0 أو أحدث
- Android Studio أو VS Code
- جهاز Android أو محاكي

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd maper
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **تشغيل التطبيق**
```bash
flutter run
```

4. **بناء APK للإنتاج**
```bash
flutter build apk --release
```

## هيكل المشروع

```
lib/
├── main.dart                 # نقطة دخول التطبيق
├── models/                   # نماذج البيانات
│   ├── mind_map.dart
│   ├── mind_map_node.dart
│   └── pdf_content.dart
├── services/                 # خدمات التطبيق
│   ├── pdf_parser_service.dart
│   ├── mind_map_converter.dart
│   └── file_manager_service.dart
├── providers/                # إدارة الحالة
│   └── mind_map_provider.dart
├── screens/                  # شاشات التطبيق
│   ├── home_screen.dart
│   ├── pdf_import_screen.dart
│   ├── mind_map_editor_screen.dart
│   └── settings_screen.dart
├── widgets/                  # مكونات واجهة المستخدم
│   ├── mind_map_canvas.dart
│   ├── mind_map_node_widget.dart
│   ├── mind_map_connection.dart
│   ├── node_editor_dialog.dart
│   ├── mind_map_card.dart
│   ├── loading_overlay.dart
│   └── error_dialog.dart
└── utils/                    # أدوات مساعدة
    └── app_theme.dart
```

## التبعيات المستخدمة

### المكتبات الأساسية
- **syncfusion_flutter_pdf**: لتحليل ملفات PDF
- **provider**: لإدارة حالة التطبيق
- **file_picker**: لاختيار الملفات
- **path_provider**: للوصول إلى مجلدات النظام

### مكتبات واجهة المستخدم
- **flutter_colorpicker**: لاختيار الألوان
- **flutter_svg**: لدعم الرسوميات المتجهة
- **flutter_staggered_animations**: للحركات المتدرجة

### مكتبات التصدير والمشاركة
- **screenshot**: لالتقاط صور الشاشة
- **share_plus**: لمشاركة الملفات
- **uuid**: لإنشاء معرفات فريدة

## المشاكل المعروفة والحلول

### 1. مشكلة تحليل PDF
إذا فشل في تحليل ملف PDF معين:
- تأكد من أن الملف ليس محمياً بكلمة مرور
- تأكد من أن حجم الملف أقل من 50 ميجابايت
- جرب ملف PDF آخر للتأكد من عمل التطبيق

### 2. مشكلة الأداء
إذا كان التطبيق بطيئاً:
- أغلق التطبيقات الأخرى لتوفير ذاكرة
- استخدم ملفات PDF أصغر حجماً
- أعد تشغيل التطبيق

### 3. مشكلة العرض
إذا لم تظهر خريطة العقل بشكل صحيح:
- اضغط على زر "إعادة تعيين العرض"
- جرب تخطيط مختلف من الإعدادات
- تأكد من تفعيل وضع التحرير للتفاعل مع العقد

## المساهمة في التطوير

نرحب بالمساهمات! إذا كنت تريد المساهمة:

1. Fork المشروع
2. أنشئ branch جديد للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات:
- افتح Issue في GitHub
- راسلنا على البريد الإلكتروني
- تابعنا على وسائل التواصل الاجتماعي

## الإصدارات القادمة

### v1.1.0 (قريباً)
- دعم OCR للمستندات الممسوحة ضوئياً
- تصدير إلى تنسيقات إضافية (SVG, PDF)
- ميزات تعاون متعددة المستخدمين
- دعم اللغات المتعددة

### v1.2.0 (مخطط)
- ذكاء اصطناعي لتحسين تحويل المحتوى
- قوالب خرائط عقل جاهزة
- تكامل مع خدمات التخزين السحابي
- إصدار ويب

---

**تم تطويره بواسطة Augment Agent**

*تطبيق احترافي لتحويل المعرفة إلى خرائط عقل بصرية*
