# 🔧 دليل حل مشاكل عرض الخريطة الذهنية

## 🚨 المشكلة: لا تظهر الخريطة الذهنية

### 🔍 الأسباب المحتملة والحلول:

#### 1. **لا توجد عقد في الخريطة**
**الأعراض:**
- تظهر رسالة "لا توجد عقد في الخريطة"
- الشاشة فارغة مع خلفية ملونة

**الحلول:**
- ✅ استخدم زر "إنشاء خريطة تجريبية" في الشاشة الرئيسية
- ✅ جرب استيراد ملف PDF صغير وبسيط
- ✅ أنشئ خريطة عقل جديدة وأضف عقد يدوياً

#### 2. **مشكلة في تحليل PDF**
**الأعراض:**
- يتم استيراد PDF لكن لا تظهر عقد
- رسائل خطأ أثناء التحليل

**الحلول:**
```bash
# تحقق من سجل التطبيق
flutter logs
```
- استخدم ملف PDF يحتوي على نص واضح
- تجنب ملفات PDF المحمية أو المشفرة
- جرب ملف PDF أصغر من 10 ميجابايت

#### 3. **مشكلة في تحديد المواضع**
**الأعراض:**
- العقد موجودة لكن خارج الشاشة
- لا يمكن رؤية العقد حتى مع التكبير

**الحلول:**
- اضغط على زر "إعادة تعيين العرض" في شريط الأدوات
- استخدم إيماءات التكبير والتصغير
- جرب تخطيط مختلف من الإعدادات

#### 4. **مشكلة في الأداء**
**الأعراض:**
- التطبيق بطيء أو يتجمد
- لا تستجيب الواجهة

**الحلول:**
- أعد تشغيل التطبيق
- امسح ذاكرة التخزين المؤقت
- تأكد من وجود ذاكرة كافية على الجهاز

### 🧪 خطوات التشخيص:

#### الخطوة 1: اختبار الخريطة التجريبية
1. افتح التطبيق
2. اضغط على "إنشاء خريطة تجريبية"
3. إذا ظهرت الخريطة ✅ المشكلة في تحليل PDF
4. إذا لم تظهر ❌ مشكلة في عرض العقد

#### الخطوة 2: فحص سجل التطبيق
```bash
# في terminal
flutter logs

# ابحث عن هذه الرسائل:
- "Building X nodes"
- "Node: [title] at position: [x, y]"
- "Total nodes built: X"
- "Positioning X children in radial layout"
```

#### الخطوة 3: اختبار PDF بسيط
1. أنشئ ملف PDF بسيط يحتوي على:
   ```
   العنوان الرئيسي
   
   العنوان الفرعي الأول
   نص تحت العنوان الأول
   
   العنوان الفرعي الثاني
   نص تحت العنوان الثاني
   ```
2. جرب استيراده

#### الخطوة 4: فحص الإعدادات
1. اذهب إلى الإعدادات
2. تحقق من معلومات التخزين
3. امسح الملفات المُصدرة إذا كانت كثيرة

### 🔧 حلول سريعة:

#### الحل السريع 1: إعادة تعيين التطبيق
```bash
# في terminal
flutter clean
flutter pub get
flutter run
```

#### الحل السريع 2: إنشاء خريطة يدوياً
1. اضغط "New Mind Map"
2. أدخل عنوان
3. فعل وضع التحرير
4. اضغط زر "+" لإضافة عقد

#### الحل السريع 3: تغيير التخطيط
1. افتح الخريطة
2. اضغط على الإعدادات (⚙️)
3. جرب تخطيط "شجري" بدلاً من "شعاعي"

### 📱 مشاكل خاصة بالأجهزة:

#### أجهزة Android القديمة:
- قلل جودة الرسوميات
- استخدم ملفات PDF أصغر
- أغلق التطبيقات الأخرى

#### أجهزة ذات ذاكرة قليلة:
- امسح ذاكرة التخزين المؤقت
- أعد تشغيل الجهاز
- استخدم ملفات أصغر

### 🆘 إذا لم تنجح الحلول:

#### تقرير المشكلة:
1. **معلومات الجهاز:**
   - نوع الجهاز
   - إصدار Android
   - مساحة الذاكرة المتاحة

2. **خطوات إعادة الإنتاج:**
   - ما فعلته بالضبط
   - نوع ملف PDF المستخدم
   - رسائل الخطأ (إن وجدت)

3. **سجل التطبيق:**
   ```bash
   flutter logs > app_log.txt
   ```

#### معلومات إضافية مفيدة:
- حجم ملف PDF
- لغة محتوى PDF
- هل يحتوي على صور أم نص فقط
- هل جربت ملفات PDF أخرى

### ✅ علامات نجاح الإصلاح:

- ✅ تظهر رسالة "Building X nodes" في السجل
- ✅ تظهر العقد على الشاشة
- ✅ يمكن التفاعل مع العقد (النقر والسحب)
- ✅ تظهر الخطوط بين العقد
- ✅ يعمل التكبير والتصغير

### 🎯 نصائح للاستخدام الأمثل:

1. **ابدأ بملفات PDF بسيطة** (1-5 صفحات)
2. **استخدم ملفات تحتوي على عناوين واضحة**
3. **تجنب ملفات PDF المعقدة** في البداية
4. **احفظ عملك بانتظام**
5. **جرب التخطيطات المختلفة** لأفضل عرض

---

**💡 تذكر:** التطبيق يعمل بشكل أفضل مع ملفات PDF التي تحتوي على نص منظم وعناوين واضحة!
