@echo off
echo ========================================
echo    Maper - Building Release APK
echo ========================================
echo.

echo Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo Error: Flutter is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo Cleaning previous builds...
flutter clean

echo.
echo Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo Error: Failed to get dependencies
    pause
    exit /b 1
)

echo.
echo Running code analysis...
flutter analyze
if %errorlevel% neq 0 (
    echo Warning: Code analysis found issues
    echo Do you want to continue? (y/n)
    set /p choice=
    if /i not "%choice%"=="y" (
        echo Build cancelled
        pause
        exit /b 1
    )
)

echo.
echo Running tests...
flutter test
if %errorlevel% neq 0 (
    echo Warning: Some tests failed
    echo Do you want to continue? (y/n)
    set /p choice=
    if /i not "%choice%"=="y" (
        echo Build cancelled
        pause
        exit /b 1
    )
)

echo.
echo Building release APK...
flutter build apk --release
if %errorlevel% neq 0 (
    echo Error: Failed to build release APK
    pause
    exit /b 1
)

echo.
echo Building app bundle...
flutter build appbundle --release
if %errorlevel% neq 0 (
    echo Error: Failed to build app bundle
    pause
    exit /b 1
)

echo.
echo ========================================
echo Release build completed successfully!
echo.
echo APK location: build\app\outputs\flutter-apk\app-release.apk
echo Bundle location: build\app\outputs\bundle\release\app-release.aab
echo ========================================
echo.

echo Opening output directory...
start build\app\outputs\flutter-apk\

pause
