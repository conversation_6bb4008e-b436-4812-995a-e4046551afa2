@echo off
echo ========================================
echo    Maper - PDF to Mind Map Converter
echo ========================================
echo.

echo Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo Error: Flutter is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo Error: Failed to get dependencies
    pause
    exit /b 1
)

echo.
echo Analyzing code...
flutter analyze
if %errorlevel% neq 0 (
    echo Warning: Code analysis found issues
    echo Continuing anyway...
)

echo.
echo Building debug APK...
flutter build apk --debug
if %errorlevel% neq 0 (
    echo Error: Failed to build APK
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo APK location: build\app\outputs\flutter-apk\app-debug.apk
echo ========================================
echo.

echo Do you want to run the app on connected device? (y/n)
set /p choice=
if /i "%choice%"=="y" (
    echo.
    echo Running app on device...
    flutter run
)

pause
